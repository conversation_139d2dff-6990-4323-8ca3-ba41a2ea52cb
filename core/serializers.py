from rest_framework import serializers
from .models import Categoria, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>o, Movimiento

class CategoriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Categoria
        fields = "__all__"

class ProveedorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Proveedor
        fields = "__all__"

class BodegaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bodega
        fields = "__all__"

class ProductoSerializer(serializers.ModelSerializer):
    categoria_nombre = serializers.CharField(source="categoria.nombre", read_only=True)
    proveedor_nombre = serializers.Char<PERSON>ield(source="proveedor.razon_social", read_only=True)
    class Meta:
        model = Producto
        fields = ["id","sku","nombre","categoria","categoria_nombre",
                  "proveedor","proveedor_nombre","precio","stock_actual"]

class MovimientoSerializer(serializers.ModelSerializer):
    producto_sku = serializers.<PERSON><PERSON><PERSON><PERSON>(source="producto.sku", read_only=True)
    class Meta:
        model = Movimiento
        fields = ["id","producto","producto_sku","bodega","tipo","cantidad","fecha","observacion"]
        read_only_fields = ["fecha"]
