

# Register your models here.
from django.contrib import admin
from .models import Categoria, <PERSON>veedor, Bodega, Producto, Movimiento

@admin.register(Categoria)
class CategoriaAdmin(admin.ModelAdmin):
    list_display = ("id", "nombre", "descripcion")
    search_fields = ("nombre",)

@admin.register(Proveedor)
class ProveedorAdmin(admin.ModelAdmin):
    list_display = ("id", "razon_social", "rut", "email", "telefono")
    search_fields = ("razon_social", "rut", "email")

@admin.register(Bodega)
class BodegaAdmin(admin.ModelAdmin):
    list_display = ("id", "nombre", "ubicacion")
    search_fields = ("nombre", "ubicacion")

@admin.register(Producto)
class ProductoAdmin(admin.ModelAdmin):
    list_display = ("id", "sku", "nombre", "categoria", "proveedor", "precio", "stock_actual")
    search_fields = ("sku", "nombre")
    list_filter = ("categoria", "proveedor")

@admin.register(Movimiento)
class MovimientoAdmin(admin.ModelAdmin):
    list_display = ("id", "producto", "bodega", "tipo", "cantidad", "fecha")
    search_fields = ("producto__nombre", "producto__sku")
    list_filter = ("tipo", "bodega", "producto")
