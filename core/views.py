

# Create your views here.
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Categoria, Proveedor, Bodega, Producto, Movimiento
from .serializers import (CategoriaSerializer, ProveedorSerializer, BodegaSerializer,
                          ProductoSerializer, MovimientoSerializer)

class CategoriaViewSet(viewsets.ModelViewSet):
    queryset = Categoria.objects.all().order_by("id")
    serializer_class = CategoriaSerializer

class ProveedorViewSet(viewsets.ModelViewSet):
    queryset = Proveedor.objects.all().order_by("id")
    serializer_class = ProveedorSerializer

class BodegaViewSet(viewsets.ModelViewSet):
    queryset = Bodega.objects.all().order_by("id")
    serializer_class = BodegaSerializer

class ProductoViewSet(viewsets.ModelViewSet):
    queryset = Producto.objects.all().order_by("id")
    serializer_class = ProductoSerializer

    @action(detail=True, methods=["get"])
    def historial(self, request, pk=None):
        producto = self.get_object()
        movs = Movimiento.objects.filter(producto=producto).order_by("-fecha","-id")
        page = self.paginate_queryset(movs)
        ser = MovimientoSerializer(page or movs, many=True)
        if page is not None:
            return self.get_paginated_response(ser.data)
        return Response(ser.data)

class MovimientoViewSet(viewsets.ModelViewSet):
    queryset = Movimiento.objects.select_related("producto").all().order_by("-fecha","-id")
    serializer_class = MovimientoSerializer
