

# Create your models here.
from django.db import models, transaction
from django.core.exceptions import ValidationError
from django.utils import timezone

class Categoria(models.Model):
    nombre = models.CharField(max_length=100, unique=True)
    descripcion = models.TextField(blank=True)
    def __str__(self): return self.nombre

class Proveedor(models.Model):
    razon_social = models.CharField(max_length=150)
    rut = models.CharField(max_length=20, unique=True)
    email = models.EmailField()
    telefono = models.CharField(max_length=30, blank=True)
    def __str__(self): return self.razon_social

class Bodega(models.Model):
    nombre = models.CharField(max_length=100)
    ubicacion = models.CharField(max_length=200)
    def __str__(self): return self.nombre

class Producto(models.Model):
    sku = models.CharField(max_length=50, unique=True)
    nombre = models.CharField(max_length=150)
    categoria = models.ForeignKey(Categoria, on_delete=models.PROTECT, related_name='productos')
    proveedor = models.ForeignKey(Proveedor, on_delete=models.PROTECT, related_name='productos')
    precio = models.DecimalField(max_digits=12, decimal_places=2)
    stock_actual = models.PositiveIntegerField(default=0)
    def __str__(self): return f"{self.nombre} ({self.sku})"

class Movimiento(models.Model):
    ENTRADA = 'ENTRADA'
    SALIDA = 'SALIDA'
    MERMA = 'MERMA'
    TIPO_CHOICES = [(ENTRADA, 'Entrada'), (SALIDA, 'Salida'), (MERMA, 'Merma')]

    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, related_name='movimientos')
    bodega = models.ForeignKey(Bodega, on_delete=models.PROTECT, related_name='movimientos')
    tipo = models.CharField(max_length=10, choices=TIPO_CHOICES)
    cantidad = models.PositiveIntegerField()
    fecha = models.DateTimeField(default=timezone.now)
    observacion = models.TextField(blank=True)

    def clean(self):
        if self.cantidad == 0:
            raise ValidationError("La cantidad debe ser mayor que 0.")

    def save(self, *args, **kwargs):
        # solo creaciones; evitamos editar movimientos en esta versión
        if self.pk:
            raise ValidationError("La edición de movimientos está deshabilitada.")
        with transaction.atomic():
            signo = 1 if self.tipo == self.ENTRADA else -1
            nuevo = self.producto.stock_actual + signo * self.cantidad
            if nuevo < 0:
                raise ValidationError("Stock insuficiente: no se permiten negativos.")
            self.producto.stock_actual = nuevo
            self.producto.save(update_fields=["stock_actual"])
            super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.tipo} {self.cantidad} de {self.producto}"
